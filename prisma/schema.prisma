generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id              String           @id @default(cuid())
  email           String           @unique
  emailVerified   DateTime?
  firstName       String?
  lastName        String?
  imageUrl        String?
  password        String?
  role            UserRole         @default(SUPER_ADMIN)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  accounts        Account[]
  billboards      Billboard[]
  marqueeMessages MarqueeMessage[]
  sessions        Session[]

  @@map("users")
}

model Customer {
  id            String    @id @default(cuid())
  email         String    @unique
  emailVerified DateTime?
  firstName     String?
  lastName      String?
  imageUrl      String?
  image         String?
  password      String?
  phone         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  addresses     Address[]
  orders        Order[]
  reviews       Review[]

  @@map("customers")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model Address {
  id             String      @id @default(cuid())
  customerId     String
  type           AddressType @default(SHIPPING)
  firstName      String
  lastName       String
  company        String?
  addressLine1   String
  addressLine2   String?
  city           String
  province       String
  postalCode     String
  country        String      @default("South Africa")
  phone          String?
  isDefault      Boolean     @default(false)
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt
  customer       Customer    @relation(fields: [customerId], references: [id], onDelete: Cascade)
  billingOrders  Order[]     @relation("BillingAddress")
  shippingOrders Order[]     @relation("ShippingAddress")

  @@map("addresses")
}

model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  slug        String    @unique
  description String?
  imageUrl    String?
  isActive    Boolean   @default(true)
  sortOrder   Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]

  @@map("categories")
}

model Brand {
  id          String    @id @default(cuid())
  name        String    @unique
  slug        String    @unique
  description String?
  logoUrl     String?
  website     String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]

  @@map("brands")
}

model Product {
  id               String           @id @default(cuid())
  name             String
  slug             String           @unique
  description      String?
  shortDescription String?
  sku              String           @unique
  categoryId       String
  brandId          String
  status           ProductStatus    @default(DRAFT)
  isActive         Boolean          @default(true)
  isFeatured       Boolean          @default(false)
  tags             String[]
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt
  orderItems       OrderItem[]
  images           ProductImage[]
  variants         ProductVariant[]
  brand            Brand            @relation(fields: [brandId], references: [id])
  category         Category         @relation(fields: [categoryId], references: [id])
  reviews          Review[]

  @@map("products")
}

model ProductVariant {
  id                String      @id @default(cuid())
  productId         String
  size              String
  color             String
  material          String?
  sku               String      @unique
  price             Decimal     @db.Decimal(10, 2)
  comparePrice      Decimal?    @db.Decimal(10, 2)
  costPrice         Decimal?    @db.Decimal(10, 2)
  stock             Int         @default(0)
  lowStockThreshold Int         @default(5)
  weight            Decimal?    @db.Decimal(8, 2)
  isActive          Boolean     @default(true)
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt
  orderItems        OrderItem[]
  product           Product     @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([productId, size, color])
  @@map("product_variants")
}

model ProductImage {
  id        String   @id @default(cuid())
  productId String
  url       String
  altText   String?
  sortOrder Int      @default(0)
  isPrimary Boolean  @default(false)
  createdAt DateTime @default(now())
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model Order {
  id                String         @id @default(cuid())
  orderNumber       String         @unique
  customerId        String
  status            OrderStatus    @default(PENDING)
  paymentStatus     PaymentStatus  @default(PENDING)
  shippingStatus    ShippingStatus @default(PENDING)
  subtotal          Decimal        @db.Decimal(10, 2)
  taxAmount         Decimal        @db.Decimal(10, 2)
  shippingAmount    Decimal        @db.Decimal(10, 2)
  discountAmount    Decimal        @default(0) @db.Decimal(10, 2)
  totalAmount       Decimal        @db.Decimal(10, 2)
  shippingAddressId String
  billingAddressId  String
  trackingNumber    String?
  shippedAt         DateTime?
  deliveredAt       DateTime?
  customerNotes     String?
  adminNotes        String?
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
  items             OrderItem[]
  billingAddress    Address        @relation("BillingAddress", fields: [billingAddressId], references: [id])
  customer          Customer       @relation(fields: [customerId], references: [id])
  shippingAddress   Address        @relation("ShippingAddress", fields: [shippingAddressId], references: [id])
  payments          Payment[]

  @@map("orders")
}

model OrderItem {
  id               String         @id @default(cuid())
  orderId          String
  productId        String
  productVariantId String
  quantity         Int
  unitPrice        Decimal        @db.Decimal(10, 2)
  totalPrice       Decimal        @db.Decimal(10, 2)
  productName      String
  productSku       String
  variantSize      String
  variantColor     String
  createdAt        DateTime       @default(now())
  order            Order          @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product          Product        @relation(fields: [productId], references: [id])
  productVariant   ProductVariant @relation(fields: [productVariantId], references: [id])

  @@map("order_items")
}

model Payment {
  id              String        @id @default(cuid())
  orderId         String
  amount          Decimal       @db.Decimal(10, 2)
  currency        String        @default("ZAR")
  method          PaymentMethod
  status          PaymentStatus @default(PENDING)
  transactionId   String?
  gatewayResponse Json?
  processedAt     DateTime?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  order           Order         @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("payments")
}

model Review {
  id         String   @id @default(cuid())
  customerId String
  productId  String
  rating     Int
  title      String?
  comment    String?
  isVerified Boolean  @default(false)
  isApproved Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  customer   Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)
  product    Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([customerId, productId])
  @@map("reviews")
}

model AnalyticsEvent {
  id            String             @id @default(cuid())
  eventType     AnalyticsEventType
  customerId    String?
  sessionId     String?
  productId     String?
  orderId       String?
  data          Json?
  customerAgent String?
  ipAddress     String?
  createdAt     DateTime           @default(now())

  @@map("analytics_events")
}

model MarqueeMessage {
  id        String      @id @default(cuid())
  title     String
  message   String
  type      MarqueeType @default(INFO)
  priority  Int         @default(1)
  isActive  Boolean     @default(true)
  startDate DateTime?
  endDate   DateTime?
  createdBy String
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  creator   User        @relation(fields: [createdBy], references: [id])

  @@map("marquee_messages")
}

model Billboard {
  id          String            @id @default(cuid())
  title       String
  description String?
  imageUrl    String?
  videoUrl    String?
  linkUrl     String?
  linkText    String?
  type        BillboardType     @default(PROMOTIONAL)
  position    BillboardPosition @default(HEADER)
  isActive    Boolean           @default(true)
  startDate   DateTime?
  endDate     DateTime?
  sortOrder   Int               @default(0)
  createdBy   String
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  creator     User              @relation(fields: [createdBy], references: [id])

  @@map("billboards")
}

enum UserRole {
  SUPER_ADMIN
  ADMIN
  MANAGER
  STAFF
}

enum AddressType {
  SHIPPING
  BILLING
  BOTH
}

enum ProductStatus {
  DRAFT
  ACTIVE
  INACTIVE
  ARCHIVED
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

enum ShippingStatus {
  PENDING
  PROCESSING
  SHIPPED
  IN_TRANSIT
  DELIVERED
  RETURNED
}

enum PaymentMethod {
  CREDIT_CARD
  DEBIT_CARD
  EFT
  PAYPAL
  APPLE_PAY
  GOOGLE_PAY
  CASH_ON_DELIVERY
  PAYFAST
  PAYGATE
  PEACH_PAYMENTS
  OZOW
  SNAPSCAN
  ZAPPER
  MASTERPASS
  VISA_CHECKOUT
  MOBICRED
  PAYFLEX
  LAYUP
  CAPITEC_PAY
  FNB_PAY
  NEDBANK_PAY
  STANDARD_BANK_PAY
  ABSA_PAY
  DISCOVERY_PAY
  YOCO
  PAYSTACK_ZA
  FLUTTERWAVE_ZA
}

enum AnalyticsEventType {
  PAGE_VIEW
  PRODUCT_VIEW
  ADD_TO_CART
  REMOVE_FROM_CART
  CHECKOUT_START
  CHECKOUT_COMPLETE
  PURCHASE
  SEARCH
  FILTER
  SORT
}

enum MarqueeType {
  INFO
  SUCCESS
  WARNING
  ERROR
  ALERT
  PROMOTION
  SYSTEM
  INVENTORY
  ORDER
}

enum BillboardType {
  PROMOTIONAL
  ANNOUNCEMENT
  PRODUCT_LAUNCH
  SALE
  SEASONAL
  SYSTEM_MESSAGE
  BRAND_CAMPAIGN
}

enum BillboardPosition {
  HEADER
  SIDEBAR
  FOOTER
  MODAL
  DASHBOARD_TOP
  DASHBOARD_BOTTOM
  PRODUCT_PAGE
  CHECKOUT
}
