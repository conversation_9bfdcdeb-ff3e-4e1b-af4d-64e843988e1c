
[20:25:31.135] > mzansi-footwear-shop@1.0.0 postinstall /vercel/path0
[20:25:31.136] > prisma generate
[20:25:31.136] 
[20:25:32.656] Prisma schema loaded from prisma/schema.prisma
[20:25:33.253] 
[20:25:33.254] ✔ Generated Prisma Client (v6.12.0) to ./node_modules/.pnpm/@prisma+client@6.12.0_prisma@6.12.0_typescript@5.7.2__typescript@5.7.2/node_modules/@prisma/client in 338ms
[20:25:33.254] 
[20:25:33.254] Start by importing your Prisma Client (See: https://pris.ly/d/importing-client)
[20:25:33.255] 
[20:25:33.255] Tip: Want to turn off tips and other hints? https://pris.ly/tip-4-nohints
[20:25:33.255] 
[20:25:33.270] 
[20:25:33.271] > mzansi-footwear-shop@1.0.0 prepare /vercel/path0
[20:25:33.271] > husky
[20:25:33.271] 
[20:25:33.347] Done in 24.7s using pnpm v10.13.1
[20:25:33.381] Detected Next.js version: 15.3.2
[20:25:33.440] Running "pnpm run build"
[20:25:33.728] 
[20:25:33.729] > mzansi-footwear-shop@1.0.0 build /vercel/path0
[20:25:33.729] > next build
[20:25:33.729] 
[20:25:35.766] Attention: Next.js now collects completely anonymous telemetry regarding usage.
[20:25:35.767] This information is used to shape Next.js' roadmap and prioritize features.
[20:25:35.767] You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[20:25:35.767] https://nextjs.org/telemetry
[20:25:35.767] 
[20:25:35.869]    ▲ Next.js 15.3.2
[20:25:35.870] 
[20:25:35.899]    Creating an optimized production build ...
[20:25:55.980]  ⚠ Compiled with warnings in 3.0s
[20:25:55.981] 
[20:25:55.981] ./node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/index.js
[20:25:55.981] A Node.js module is loaded ('crypto' at line 32) which is not supported in the Edge Runtime.
[20:25:55.981] Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime
[20:25:55.982] 
[20:25:55.982] Import trace for requested module:
[20:25:55.982] ./node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/index.js
[20:25:55.982] ./src/lib/auth.ts
[20:25:55.982] 
[20:25:55.983] ./node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/index.js
[20:25:55.983] A Node.js API is used (process.nextTick at line: 337) which is not supported in the Edge Runtime.
[20:25:55.985] Learn more: https://nextjs.org/docs/api-reference/edge-runtime
[20:25:55.986] 
[20:25:55.986] Import trace for requested module:
[20:25:55.986] ./node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/index.js
[20:25:55.986] ./src/lib/auth.ts
[20:25:55.986] 
[20:25:55.987] ./node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/index.js
[20:25:55.987] A Node.js API is used (setImmediate at line: 338) which is not supported in the Edge Runtime.
[20:25:55.987] Learn more: https://nextjs.org/docs/api-reference/edge-runtime
[20:25:55.987] 
[20:25:55.987] Import trace for requested module:
[20:25:55.988] ./node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/index.js
[20:25:55.988] ./src/lib/auth.ts
[20:25:55.988] 
[20:25:55.988] ./node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/index.js
[20:25:55.988] A Node.js API is used (setImmediate at line: 339) which is not supported in the Edge Runtime.
[20:25:55.988] Learn more: https://nextjs.org/docs/api-reference/edge-runtime
[20:25:55.988] 
[20:25:55.989] Import trace for requested module:
[20:25:55.989] ./node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/index.js
[20:25:55.989] ./src/lib/auth.ts
[20:25:55.989] 
[20:25:55.989] ./node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/index.js
[20:25:55.989] A Node.js API is used (process.nextTick at line: 340) which is not supported in the Edge Runtime.
[20:25:55.989] Learn more: https://nextjs.org/docs/api-reference/edge-runtime
[20:25:55.990] 
[20:25:55.990] Import trace for requested module:
[20:25:55.990] ./node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/index.js
[20:25:55.990] ./src/lib/auth.ts
[20:25:55.990] 
[20:26:08.945]  ✓ Compiled successfully in 30.0s
[20:26:08.951]    Linting and checking validity of types ...
[20:26:24.982] Failed to compile.
[20:26:24.982] 
[20:26:24.983] ./src/lib/services/orders.ts:263:12
[20:26:24.983] Type error: Conversion of type 'Prisma__OrderClient<{ customer: { password: string | null; id: string; email: string; emailVerified: Date | null; firstName: string | null; lastName: string | null; imageUrl: string | null; createdAt: Date; updatedAt: Date; image: string | null; phone: string | null; }; items: ({ ...; } & { ...; })[]; billingAddress...' to type 'Promise<OrderWithDetails>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
[20:26:24.983]   Types of property 'then' are incompatible.
[20:26:24.983]     Type '<TResult1 = { customer: { password: string | null; id: string; email: string; emailVerified: Date | null; firstName: string | null; lastName: string | null; imageUrl: string | null; createdAt: Date; updatedAt: Date; image: string | null; phone: string | null; }; items: ({ ...; } & { ...; })[]; billingAddress: { ...;...' is not comparable to type '<TResult1 = OrderWithDetails, TResult2 = never>(onfulfilled?: ((value: OrderWithDetails) => TResult1 | PromiseLike<TResult1>) | null | undefined, onrejected?: ((reason: any) => TResult2 | PromiseLike<...>) | ... 1 more ... | undefined) => Promise<...>'.
[20:26:24.983]       Types of parameters 'onfulfilled' and 'onfulfilled' are incompatible.
[20:26:24.983]         Types of parameters 'value' and 'value' are incompatible.
[20:26:24.983]           Type '{ customer: { password: string | null; id: string; email: string; emailVerified: Date | null; firstName: string | null; lastName: string | null; imageUrl: string | null; createdAt: Date; updatedAt: Date; image: string | null; phone: string | null; }; items: ({ ...; } & { ...; })[]; billingAddress: { ...; }; shipping...' is not comparable to type 'OrderWithDetails'.
[20:26:24.983]             Type '{ customer: { password: string | null; id: string; email: string; emailVerified: Date | null; firstName: string | null; lastName: string | null; imageUrl: string | null; createdAt: Date; updatedAt: Date; image: string | null; phone: string | null; }; items: ({ ...; } & { ...; })[]; billingAddress: { ...; }; shipping...' is not comparable to type '{ customer: { password: string | null; id: string; email: string; emailVerified: Date | null; firstName: string | null; lastName: string | null; imageUrl: string | null; createdAt: Date; updatedAt: Date; image: string | null; phone: string | null; }; shippingAddress?: { ...; } | ... 1 more ... | undefined; billingAd...'.
[20:26:24.983]               Types of property 'items' are incompatible.
[20:26:24.983]                 Type '({ product: { name: string; slug: string; images: { url: string; }[]; }; productVariant: { sku: string; size: string; color: string; }; } & { id: string; createdAt: Date; productId: string; orderId: string; ... 7 more ...; variantColor: string; })[]' is not comparable to type '({ id: string; createdAt: Date; productId: string; orderId: string; productVariantId: string; quantity: number; unitPrice: Decimal; totalPrice: Decimal; productName: string; productSku: string; variantSize: string; variantColor: string; } & { ...; })[]'.
[20:26:24.983]                   Type '{ product: { name: string; slug: string; images: { url: string; }[]; }; productVariant: { sku: string; size: string; color: string; }; } & { id: string; createdAt: Date; productId: string; orderId: string; ... 7 more ...; variantColor: string; }' is not comparable to type '{ id: string; createdAt: Date; productId: string; orderId: string; productVariantId: string; quantity: number; unitPrice: Decimal; totalPrice: Decimal; productName: string; productSku: string; variantSize: string; variantColor: string; } & { ...; }'.
[20:26:24.983]                     Type '{ product: { name: string; slug: string; images: { url: string; }[]; }; productVariant: { sku: string; size: string; color: string; }; } & { id: string; createdAt: Date; productId: string; orderId: string; ... 7 more ...; variantColor: string; }' is not comparable to type '{ product: { name: string; slug: string; images: { url: string; }[]; category: { id: string; name: string; imageUrl: string | null; createdAt: Date; updatedAt: Date; description: string | null; isActive: boolean; sortOrder: number; slug: string; }; brand: { ...; }; }; productVariant?: { ...; } | ... 1 more ... | und...'.
[20:26:24.983]                       Types of property 'product' are incompatible.
[20:26:24.983]                         Type '{ name: string; slug: string; images: { url: string; }[]; }' is missing the following properties from type '{ name: string; slug: string; images: { url: string; }[]; category: { id: string; name: string; imageUrl: string | null; createdAt: Date; updatedAt: Date; description: string | null; isActive: boolean; sortOrder: number; slug: string; }; brand: { ...; }; }': category, brand
[20:26:24.983] 
[20:26:24.983] [0m [90m 261 |[39m     deliveredAt[33m?[39m[33m:[39m [33mDate[39m[0m
[20:26:24.983] [0m [90m 262 |[39m   }) {[0m
[20:26:24.983] [0m[31m[1m>[22m[39m[90m 263 |[39m     [36mreturn[39m db[33m.[39morder[33m.[39mupdate({[0m
[20:26:24.984] [0m [90m     |[39m            [31m[1m^[22m[39m[0m
[20:26:24.984] [0m [90m 264 |[39m       where[33m:[39m { id[33m:[39m orderId }[33m,[39m[0m
[20:26:24.984] [0m [90m 265 |[39m       data[33m:[39m {[0m
[20:26:24.984] [0m [90m 266 |[39m         [33m...[39mdata[33m,[39m[0m
[20:26:25.022] Next.js build worker exited with code: 1 and signal: null
[20:26:25.044]  ELIFECYCLE  Command failed with exit code 1.
[20:26:25.064] Error: Command "pnpm run build" exited with 1
[20:26:25.556] 
[20:26:29.198] Exiting build container
