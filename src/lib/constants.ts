export type SortFilterItem = {
  title: string;
  slug: string | null;
  sortKey: 'RELEVANCE' | 'BEST_SELLING' | 'CREATED_AT' | 'PRICE';
  reverse: boolean;
};

export const defaultSort: SortFilterItem = {
  title: 'Relevance',
  slug: null,
  sortKey: 'RELEVANCE',
  reverse: false
};

export const sorting: SortFilterItem[] = [
  defaultSort,
  { title: 'Trending', slug: 'trending-desc', sortKey: 'BEST_SELLING', reverse: false },
  { title: 'Latest arrivals', slug: 'latest-desc', sortKey: 'CREATED_AT', reverse: true },
  { title: 'Price: Low to high', slug: 'price-asc', sortKey: 'PRICE', reverse: false },
  { title: 'Price: High to low', slug: 'price-desc', sortKey: 'PRICE', reverse: true }
];

export const TAGS = {
  categories: 'categories',
  products: 'products',
  cart: 'cart'
};

export const DEFAULT_OPTION = 'Default Title';

// App configuration
export const APP_CONFIG = {
  name: '<PERSON><PERSON>si Footwear',
  description: 'Premium South African Footwear',
  currency: 'ZAR',
  locale: 'en-ZA',
  itemsPerPage: 12,
  maxCartItems: 50
};
