import Header from '@/components/layout/header';
import { StoreFooter } from '@/components/layout/store-footer';
import { CartView } from '@/components/cart/cart-view';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, B<PERSON><PERSON><PERSON>bPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';

export const metadata = {
  title: 'Shopping Cart - Mzansi Footwear',
  description: 'Review your selected items and proceed to checkout.',
};

export default function CartPage() {
  return (
    <>
      <Header />
      <main className="flex-1">
        {/* Breadcrumbs */}
        <div className="bg-gray-50 border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Shopping Cart</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </div>

        {/* Cart Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <CartView />
        </div>
      </main>
      <StoreFooter />
    </>
  );
}
